package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.TweetAnalysisResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 推文分析服务
 * 使用Gemini AI分析推文内容，判断是否包含铸造/mint相关内容
 */
@Service
@Slf4j
public class TweetAnalysisService {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private static GoogleAiGeminiChatModel googleAiGeminiChatModel;

    // URL匹配正则表达式
    private static final Pattern URL_PATTERN = Pattern.compile(
        "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+",
        Pattern.CASE_INSENSITIVE
    );

    // mint相关关键词
    private static final List<String> MINT_KEYWORDS = Arrays.asList(
        "mint", "minting", "铸造", "铸币", "mint now", "mint live", "mint soon",
        "free mint", "public mint", "whitelist mint", "presale mint",
        "mint price", "mint cost", "mint fee", "mint开始", "开始mint",
        "正在mint", "mint中", "mint活动", "mint项目", "nft mint", "token mint"
    );

    // 垃圾内容关键词
    private static final List<String> SPAM_KEYWORDS = Arrays.asList(
        "follow me", "关注我", "点赞", "转发", "retweet", "like and retweet",
        "giveaway", "抽奖", "免费送", "空投", "airdrop", "follow for follow",
        "f4f", "互关", "互粉", "求关注", "求粉", "广告", "推广", "promotion"
    );

    /**
     * 分析推文内容
     */
    public TweetAnalysisResult analyzeTweet(String tweetContent) {
        log.info("开始分析推文: {}", tweetContent.substring(0, Math.min(100, tweetContent.length())));

        try {
            // 基础检查
            boolean containsUrl = containsUrl(tweetContent);
            List<String> extractedUrls = extractUrls(tweetContent);
            boolean containsMintKeywords = containsMintKeywords(tweetContent);
            List<String> foundMintKeywords = findMintKeywords(tweetContent);
            boolean isSpam = isSpamContent(tweetContent);

            // 如果没有URL或没有mint关键词，直接返回不符合条件
            if (!containsUrl || !containsMintKeywords) {
                return TweetAnalysisResult.builder()
                    .containsMintContent(containsMintKeywords)
                    .containsUrl(containsUrl)
                    .isSpam(isSpam)
                    .shouldNotify(false)
                    .confidence(90)
                    .extractedUrls(extractedUrls)
                    .mintKeywords(foundMintKeywords)
                    .analysisDetails("基础检查未通过")
                    .reason(!containsUrl ? "不包含网址链接" : "不包含mint相关关键词")
                    .originalContent(tweetContent)
                    .build();
            }

            // 使用AI进行深度分析
            String aiAnalysis = performAIAnalysis(tweetContent);
            TweetAnalysisResult aiResult = parseAIAnalysis(aiAnalysis, tweetContent);

            // 综合判断
            boolean shouldNotify = aiResult.isShouldNotify() && !isSpam && containsUrl && containsMintKeywords;

            return TweetAnalysisResult.builder()
                .containsMintContent(containsMintKeywords)
                .containsUrl(containsUrl)
                .isSpam(isSpam)
                .shouldNotify(shouldNotify)
                .confidence(aiResult.getConfidence())
                .extractedUrls(extractedUrls)
                .mintKeywords(foundMintKeywords)
                .analysisDetails(aiResult.getAnalysisDetails())
                .reason(shouldNotify ? "符合mint推送条件" : aiResult.getReason())
                .originalContent(tweetContent)
                .build();

        } catch (Exception e) {
            log.error("分析推文时发生错误", e);
            return TweetAnalysisResult.builder()
                .containsMintContent(false)
                .containsUrl(false)
                .isSpam(false)
                .shouldNotify(false)
                .confidence(0)
                .extractedUrls(new ArrayList<>())
                .mintKeywords(new ArrayList<>())
                .analysisDetails("分析过程中发生错误: " + e.getMessage())
                .reason("系统错误")
                .originalContent(tweetContent)
                .build();
        }
    }

    /**
     * 检查是否包含URL
     */
    private boolean containsUrl(String content) {
        return URL_PATTERN.matcher(content).find();
    }

    /**
     * 提取所有URL
     */
    private List<String> extractUrls(String content) {
        List<String> urls = new ArrayList<>();
        Matcher matcher = URL_PATTERN.matcher(content);
        while (matcher.find()) {
            urls.add(matcher.group());
        }
        return urls;
    }

    /**
     * 检查是否包含mint关键词
     */
    private boolean containsMintKeywords(String content) {
        String lowerContent = content.toLowerCase();
        return MINT_KEYWORDS.stream().anyMatch(keyword -> 
            lowerContent.contains(keyword.toLowerCase()));
    }

    /**
     * 查找包含的mint关键词
     */
    private List<String> findMintKeywords(String content) {
        List<String> foundKeywords = new ArrayList<>();
        String lowerContent = content.toLowerCase();
        for (String keyword : MINT_KEYWORDS) {
            if (lowerContent.contains(keyword.toLowerCase())) {
                foundKeywords.add(keyword);
            }
        }
        return foundKeywords;
    }

    /**
     * 检查是否为垃圾内容
     */
    private boolean isSpamContent(String content) {
        String lowerContent = content.toLowerCase();
        return SPAM_KEYWORDS.stream().anyMatch(keyword -> 
            lowerContent.contains(keyword.toLowerCase()));
    }

    /**
     * 使用AI进行深度分析
     */
    private String performAIAnalysis(String tweetContent) {
        String prompt = String.format(
            "请分析以下推文内容，判断是否是关于NFT或代币铸造(mint)的重要信息：\n\n" +
            "推文内容：%s\n\n" +
            "请按照以下JSON格式回复：\n" +
            "{\n" +
            "  \"isMintRelated\": true/false,\n" +
            "  \"isImportant\": true/false,\n" +
            "  \"confidence\": 0-100,\n" +
            "  \"reason\": \"分析原因\",\n" +
            "  \"details\": \"详细分析\"\n" +
            "}\n\n" +
            "判断标准：\n" +
            "1. 是否真的在进行NFT或代币铸造活动\n" +
            "2. 是否包含有效的铸造链接或官方网站\n" +
            "3. 是否为垃圾信息、广告或无关内容\n" +
            "4. 信息的时效性和重要性",
            tweetContent
        );

        try {
            return chat(prompt);
        } catch (Exception e) {
            log.error("AI分析失败", e);
            return "{\"isMintRelated\": false, \"isImportant\": false, \"confidence\": 0, \"reason\": \"AI分析失败\", \"details\": \"" + e.getMessage() + "\"}";
        }
    }

    public static String chat(String prompt) {
        try {
            return googleAiGeminiChatModel.chat(prompt);
        } catch (Exception e) {
            log.error("AI分析失败", e);
            return "{\"isMintRelated\": false, \"isImportant\": false, \"confidence\": 0, \"reason\": \"AI分析失败\", \"details\": \"" + e.getMessage() + "\"}";
        }
    }

    /**
     * 解析AI分析结果
     */
    private TweetAnalysisResult parseAIAnalysis(String aiResponse, String originalContent) {
        try {
            // 尝试提取JSON部分
            String jsonPart = extractJsonFromResponse(aiResponse);
            JsonNode jsonNode = objectMapper.readTree(jsonPart);

            boolean isMintRelated = jsonNode.path("isMintRelated").asBoolean(false);
            boolean isImportant = jsonNode.path("isImportant").asBoolean(false);
            int confidence = jsonNode.path("confidence").asInt(0);
            String reason = jsonNode.path("reason").asText("未知原因");
            String details = jsonNode.path("details").asText("无详细信息");

            boolean shouldNotify = isMintRelated && isImportant && confidence >= 70;

            return TweetAnalysisResult.builder()
                .shouldNotify(shouldNotify)
                .confidence(confidence)
                .analysisDetails(details)
                .reason(reason)
                .originalContent(originalContent)
                .build();

        } catch (Exception e) {
            log.error("解析AI响应失败: {}", aiResponse, e);
            return TweetAnalysisResult.builder()
                .shouldNotify(false)
                .confidence(0)
                .analysisDetails("AI响应解析失败")
                .reason("解析错误: " + e.getMessage())
                .originalContent(originalContent)
                .build();
        }
    }

    /**
     * 从响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON开始和结束位置
        int startIndex = response.indexOf("{");
        int endIndex = response.lastIndexOf("}");
        
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }
        
        // 如果没有找到完整的JSON，返回默认值
        return "{\"isMintRelated\": false, \"isImportant\": false, \"confidence\": 0, \"reason\": \"无法解析AI响应\", \"details\": \"响应格式错误\"}";
    }
}
