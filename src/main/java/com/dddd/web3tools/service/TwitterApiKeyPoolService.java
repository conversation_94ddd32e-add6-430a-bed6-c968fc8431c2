package com.dddd.web3tools.service;

import com.dddd.web3tools.config.TwitterApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Twitter API Key池管理服务
 * 负责管理多个API Key的轮换和使用计数
 */
@Slf4j
@Service
public class TwitterApiKeyPoolService {
    
    private static final String REDIS_KEY_PREFIX = "twitter:api:key:";
    private static final String CURRENT_KEY_INDEX = "twitter:api:current_index";
    
    @Autowired
    private TwitterApiConfig twitterApiConfig;
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    private final AtomicInteger currentKeyIndex = new AtomicInteger(0);
    private boolean redisAvailable = false;

    @PostConstruct
    public void init() {
        try {
            if (redisTemplate != null) {
                // 测试Redis连接
                redisTemplate.opsForValue().get("test");
                redisAvailable = true;

                // 初始化当前key索引
                Object currentIndex = redisTemplate.opsForValue().get(CURRENT_KEY_INDEX);
                if (currentIndex != null) {
                    currentKeyIndex.set((Integer) currentIndex);
                }
                log.info("Twitter API Key池服务初始化完成，Redis可用，当前key索引: {}", currentKeyIndex.get());
            } else {
                log.warn("Redis不可用，Twitter API Key池将使用内存模式");
            }
        } catch (Exception e) {
            log.warn("Redis连接失败，Twitter API Key池将使用内存模式: {}", e.getMessage());
            redisAvailable = false;
        }
    }
    
    /**
     * 获取当前可用的API Key
     * @return 当前API Key
     */
    public String getCurrentApiKey() {
        if (!twitterApiConfig.getPool().isEnabled() || 
            twitterApiConfig.getPool().getKeys() == null || 
            twitterApiConfig.getPool().getKeys().isEmpty()) {
            log.warn("API Key池未启用或未配置，返回空");
            return null;
        }
        
        List<String> keys = twitterApiConfig.getPool().getKeys();
        int index = currentKeyIndex.get() % keys.size();
        String currentKey = keys.get(index);
        
        // 检查当前key是否已达到使用限制
        if (isKeyExhausted(currentKey)) {
            // 切换到下一个key
            switchToNextKey();
            index = currentKeyIndex.get() % keys.size();
            currentKey = keys.get(index);
        }
        
        return currentKey;
    }
    
    /**
     * 记录API Key使用次数
     * @param apiKey 使用的API Key
     */
    public void recordKeyUsage(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return;
        }

        if (!redisAvailable || redisTemplate == null) {
            log.debug("Redis不可用，跳过API Key使用计数");
            return;
        }

        try {
            String redisKey = REDIS_KEY_PREFIX + apiKey;
            Long currentCount = redisTemplate.opsForValue().increment(redisKey);

            // 设置过期时间（根据配置的重置间隔）
            if (currentCount == 1) {
                Duration expiration = Duration.ofHours(twitterApiConfig.getPool().getResetIntervalHours());
                redisTemplate.expire(redisKey, expiration);
            }

            log.debug("API Key {} 使用次数: {}", maskApiKey(apiKey), currentCount);

            // 检查是否需要切换key
            if (currentCount >= twitterApiConfig.getPool().getMaxRequestsPerKey()) {
                log.warn("API Key {} 已达到使用限制 {}，将在下次请求时切换",
                        maskApiKey(apiKey), twitterApiConfig.getPool().getMaxRequestsPerKey());
            }
        } catch (Exception e) {
            log.error("记录API Key使用次数失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查API Key是否已耗尽
     * @param apiKey API Key
     * @return 是否已耗尽
     */
    private boolean isKeyExhausted(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return true;
        }

        if (!redisAvailable || redisTemplate == null) {
            return false; // Redis不可用时，不限制使用
        }

        try {
            String redisKey = REDIS_KEY_PREFIX + apiKey;
            Object count = redisTemplate.opsForValue().get(redisKey);

            if (count == null) {
                return false;
            }

            int currentCount = (Integer) count;
            return currentCount >= twitterApiConfig.getPool().getMaxRequestsPerKey();
        } catch (Exception e) {
            log.error("检查API Key使用状态失败: {}", e.getMessage());
            return false; // 出错时不限制使用
        }
    }
    
    /**
     * 切换到下一个API Key
     */
    private synchronized void switchToNextKey() {
        List<String> keys = twitterApiConfig.getPool().getKeys();
        if (keys == null || keys.isEmpty()) {
            return;
        }

        int nextIndex = (currentKeyIndex.get() + 1) % keys.size();
        currentKeyIndex.set(nextIndex);

        // 保存到Redis（如果可用）
        if (redisAvailable && redisTemplate != null) {
            try {
                redisTemplate.opsForValue().set(CURRENT_KEY_INDEX, nextIndex);
            } catch (Exception e) {
                log.error("保存当前key索引到Redis失败: {}", e.getMessage());
            }
        }

        String newKey = keys.get(nextIndex);
        log.info("切换到新的API Key: {}", maskApiKey(newKey));
    }
    
    /**
     * 获取API Key使用统计
     * @param apiKey API Key
     * @return 使用次数
     */
    public int getKeyUsageCount(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return 0;
        }

        if (!redisAvailable || redisTemplate == null) {
            return 0;
        }

        try {
            String redisKey = REDIS_KEY_PREFIX + apiKey;
            Object count = redisTemplate.opsForValue().get(redisKey);
            return count != null ? (Integer) count : 0;
        } catch (Exception e) {
            log.error("获取API Key使用统计失败: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * 获取所有API Key的使用统计
     * @return 统计信息
     */
    public String getPoolStatus() {
        if (!twitterApiConfig.getPool().isEnabled() || 
            twitterApiConfig.getPool().getKeys() == null || 
            twitterApiConfig.getPool().getKeys().isEmpty()) {
            return "API Key池未启用或未配置";
        }
        
        StringBuilder status = new StringBuilder();
        status.append("Twitter API Key池状态:\n");
        
        List<String> keys = twitterApiConfig.getPool().getKeys();
        int currentIndex = currentKeyIndex.get() % keys.size();
        
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            int usage = getKeyUsageCount(key);
            String marker = (i == currentIndex) ? " [当前]" : "";
            
            status.append(String.format("Key %d: %s - 使用次数: %d/%d%s\n", 
                    i + 1, 
                    maskApiKey(key), 
                    usage, 
                    twitterApiConfig.getPool().getMaxRequestsPerKey(),
                    marker));
        }
        
        return status.toString();
    }
    
    /**
     * 重置所有API Key的使用计数
     */
    public void resetAllKeyUsage() {
        if (twitterApiConfig.getPool().getKeys() == null) {
            return;
        }

        if (!redisAvailable || redisTemplate == null) {
            log.warn("Redis不可用，无法重置API Key使用计数");
            return;
        }

        try {
            for (String key : twitterApiConfig.getPool().getKeys()) {
                String redisKey = REDIS_KEY_PREFIX + key;
                redisTemplate.delete(redisKey);
            }
            log.info("已重置所有API Key的使用计数");
        } catch (Exception e) {
            log.error("重置API Key使用计数失败: {}", e.getMessage());
        }
    }
    
    /**
     * 掩码API Key用于日志输出
     * @param apiKey 原始API Key
     * @return 掩码后的API Key
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
}
