package com.dddd.web3tools.util;

import com.dddd.web3tools.config.TwitterApiConfig;
import com.dddd.web3tools.service.TwitterApiKeyPoolService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

@Slf4j
@Component
public class TwitterAPIUtil {

    public static final HttpClient HTTP_CLIENT = HttpClient.newHttpClient();

    @Autowired
    private TwitterApiKeyPoolService keyPoolService;

    @Autowired
    private TwitterApiConfig twitterApiConfig;

    // 保持向后兼容的静态变量（已废弃）
    @Deprecated
    public static final String apiKey = "7aa357b972msh4eee09d21178af4p1b40a4jsnff9e6245cb7c";
    @Deprecated
    public static final String apiHost = "twitter-api45.p.rapidapi.com";

    /**
     * 构建URL（静态方法，保持向后兼容）
     */
    public static String buildUrl(String baseUrl, String... params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl).append("?");
        for (int i = 0; i < params.length; i += 2) {
            if (i > 0) urlBuilder.append("&");
            urlBuilder.append(params[i]).append("=").append(params[i + 1]);
        }
        return urlBuilder.toString();
    }

    /**
     * 发送HTTP请求（使用key池）
     */
    public String sendHttpRequestWithPool(String url) throws Exception {
        String currentApiKey = keyPoolService.getCurrentApiKey();
        if (currentApiKey == null) {
            log.warn("无可用的API Key，使用默认key");
            currentApiKey = apiKey;
        }

        String host = twitterApiConfig.getHost();
        if (host == null) {
            host = apiHost;
        }

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("x-rapidapi-key", currentApiKey)
                .header("x-rapidapi-host", host)
                .GET()
                .build();

        String response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString()).body();

        // 记录API Key使用次数
        keyPoolService.recordKeyUsage(currentApiKey);

        return response;
    }

    /**
     * 发送HTTP请求（静态方法，保持向后兼容，已废弃）
     */
    @Deprecated
    public static String sendHttpRequest(String url) throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("x-rapidapi-key", apiKey)
                .header("x-rapidapi-host", apiHost)
                .GET()
                .build();

        return HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString()).body();
    }

    /**
     * 获取Twitter列表用户（使用key池）
     */
    public JsonNode getTwitterListUsers(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/list_members.php", "list_id", listId);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * 获取用户推文（使用key池）
     */
    public JsonNode getUserTweets(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/timeline.php",
                            "screenname", username);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * 根据用户名获取用户信息（使用key池）
     */
    public JsonNode getUserByUsername(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/screenname.php",
                "screenname", username);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * 根据列表ID获取Twitter列表（使用key池）
     */
    public JsonNode getTwitterListByListId(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/listtimeline.php",
                "list_id", listId);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    // ========== 静态方法（保持向后兼容，已废弃） ==========

    /**
     * @deprecated 请使用实例方法 getTwitterListUsers(String listId)
     */
    @Deprecated
    public static JsonNode getTwitterListUsersStatic(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/list_members.php", "list_id", listId);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * @deprecated 请使用实例方法 getUserTweets(String username)
     */
    @Deprecated
    public static JsonNode getUserTweetsStatic(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/timeline.php",
                            "screenname", username);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * @deprecated 请使用实例方法 getUserByUsername(String username)
     */
    @Deprecated
    public static JsonNode getUserByUsernameStatic(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/screenname.php",
                "screenname", username);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * @deprecated 请使用实例方法 getTwitterListByListId(String listId)
     */
    @Deprecated
    public static JsonNode getTwitterListByListIdStatic(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/listtimeline.php",
                "list_id", listId);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

}
