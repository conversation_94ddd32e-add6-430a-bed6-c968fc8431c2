package com.dddd.web3tools.job;

import com.dddd.web3tools.dto.TweetAnalysisResult;
import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.repository.BlacklistUserRepository;
import com.dddd.web3tools.repository.KeywordRepository;
import com.dddd.web3tools.repository.TweetRepository;
import com.dddd.web3tools.service.GeminiService;
import com.dddd.web3tools.service.impl.KeywordService;
import com.dddd.web3tools.service.impl.NotificationService;
import com.dddd.web3tools.util.TwitterAPIUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class TwitterMessageCollectAndSendJob {
    @Autowired
    private TweetRepository tweetRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private KeywordService keywordService;

    @Autowired
    private KeywordRepository keywordRepository;

    @Autowired
    private BlacklistUserRepository blacklistUserRepository;

    @Autowired
    private TwitterAPIUtil twitterAPIUtil;

    @Autowired
    private GeminiService geminiService;

    @Value("${twitter.api.url}")
    private String apiUrl;

    @Value("${search.twitter.api.url}")
    private String searchApiUrl;

    @Value("${twitter.api.list_id}")
    private String listId;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String SEARCH_TYPE = "Latest";  // Top or Latest
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss Z yyyy", Locale.ENGLISH);

    private static final Logger logger = LoggerFactory.getLogger(TwitterMessageCollectAndSendJob.class);


    @Scheduled(fixedRate = 600000)
    public void fetchTwitterTimeline() {
        try {
            logger.info("开始获取Twitter时间线...");
            JsonNode response = twitterAPIUtil.getTwitterListByListId(listId);
            extractTweets(response);
        } catch (Exception e) {
            logger.error("获取Twitter时间线失败", e);
        }
    }

    @Scheduled(fixedRate = 1200000)
    public void searchTwitterByKeywords() {
        try {
            Set<String> keywords = keywordService.getKewordByBaseType();
            List<String> allTweets = new ArrayList<>();

            keywords.forEach(keyword -> {
                try {
                    logger.info("搜索关键词begin...");
                    String url = TwitterAPIUtil.buildUrl(searchApiUrl, "search_type", SEARCH_TYPE, "query", keyword);
                    String response = twitterAPIUtil.sendHttpRequestWithPool(url);
                    List<String> tweets = extractSearchTweets(objectMapper.readTree(response));
                    allTweets.addAll(tweets);
                } catch (Exception e) {
                    logger.error("搜索关键词[{}]失败", keyword, e);
                }
            });

            if (!allTweets.isEmpty()) {
                logger.info("search-总共处理以下数量的推文: {}", allTweets.size());
                processSearchTweets(allTweets);
            }
        } catch (Exception e) {
            logger.error("搜索Twitter关键词失败", e);
        }
    }


//    private void processResponse(String response,
//                                 Function<JsonNode, List<String>> extractor,
//                                 Consumer<List<String>> processor) throws Exception {
//        JsonNode rootNode = objectMapper.readTree(response);
//        List<String> tweets = extractor.apply(rootNode);
//        logger.info("总共处理以下数量的推文: {}", tweets.size());
//        if (!tweets.isEmpty()) {
//            processor.accept(tweets);
//            logger.info("总共处理以下数量的推文: {}", tweets.size());
//        }
//    }
//
    private List<String> extractTweets(JsonNode rootNode) {
        List<String> tweets = new ArrayList<>();
        if (rootNode.has("timeline")) {
            JsonNode dataNode = rootNode.get("timeline");
            for (JsonNode tweetNode : dataNode) {
                if (tweetNode.has("text") && tweetNode.get("text") != null) {
                    String tweetId = tweetNode.get("tweet_id").asText();
                    if (!tweetRepository.existsById(tweetId)) {
                        Tweet tweet = new Tweet();
                        tweet.setId(tweetId);
                        tweet.setContent(tweetNode.get("text").asText());
                        tweet.setAuthor(tweetNode.get("author").get("name").asText());
                        tweet.setScreenName(tweetNode.get("author").get("screen_name").asText());
                        if (!tweetNode.get("created_at").isNull()) {
                            tweet.setCreatedAt(
                                    ZonedDateTime.parse(tweetNode.get("created_at").asText(), formatter)
                                            .withZoneSameInstant(ZoneId.of("Asia/Shanghai"))
                                            .toLocalDateTime()
                            );
                        } else {
                            tweet.setCreatedAt(LocalDateTime.now()); // 如果created_at为null，使用当前时间
                        }
                        tweet.setType(1);
                        tweet.setState(0);
                        tweetRepository.save(tweet);
                    }
                }
            }
        }
        return tweets;
    }

    private List<String> extractSearchTweets(JsonNode rootNode) {
        List<String> tweets = new ArrayList<>();
        if (rootNode.has("timeline")) {
            JsonNode dataNode = rootNode.get("timeline");
            for (JsonNode tweetNode : dataNode) {
                if (tweetNode.has("text")) {
                    String tweetId = tweetNode.get("tweet_id").asText();
                    JsonNode authorNode = tweetNode.get("user_info");
                    String content = tweetNode.get("text").asText();

                    // 这里增加的额外判断是为了排除掉，包含了关键字，但是链接其实是一些图片或者视频的情况 并不需要
                    // 检查链接条件和 media 条件
                    boolean shouldSkip = false;
                    // 正则表达式匹配链接
                    java.util.regex.Pattern urlPattern = java.util.regex.Pattern.compile("https?://\\S+");
                    java.util.regex.Matcher matcher = urlPattern.matcher(content);
                    int urlCount = 0;
                    String lastUrl = null;
                    while (matcher.find()) {
                        urlCount++;
                        lastUrl = matcher.group();
                    }
                    if (urlCount == 1 && content.endsWith(lastUrl) && tweetNode.has("media") && !tweetNode.get("media").isNull()) {
                        shouldSkip = true;
                    }

                    if (!shouldSkip) {
                        saveTweet(tweetId, content, authorNode, authorNode.get("followers_count").asInt(), tweets, tweetNode.get("created_at").asText(),tweetNode);
                    }
                }
            }
        }
        return tweets;
    }

    private void saveTweet(String tweetId, String content, JsonNode authorNode, int followers, List<String> tweets, String tweetTime,JsonNode tweetNode) {
        String authorId = authorNode.get("screen_name").asText();
        // 修改：新增关键词检查条件
        if (!tweetRepository.existsById(tweetId) &&
                notificationService.shouldSaveTweet(content, followers,authorId)) {  // 新增关键词检查
            Tweet tweet = new Tweet();
            tweet.setId(tweetId);
            tweet.setContent(content);
            tweet.setAuthor(authorNode.get("name").asText());
            tweet.setFollowers(followers);

//            // 转换时间格式为UTF+8时区
//            DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss Z yyyy", Locale.ENGLISH);
//            DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
//            ZonedDateTime utcTime = ZonedDateTime.parse(tweetTime, inputFormat);
//            ZonedDateTime beijingTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
//            tweet.setTweetTime(beijingTime.format(outputFormat));

//            // 调用DeepSeek API进行分析
//            String aiResponse = callDeepSeekAPI(content);
//            tweet.setAiAnalysis(aiResponse);

            if (!tweetNode.get("created_at").isNull()) {
                tweet.setCreatedAt(
                        ZonedDateTime.parse(tweetNode.get("created_at").asText(), formatter)
                                .withZoneSameInstant(ZoneId.of("Asia/Shanghai"))
                                .toLocalDateTime()
                );
            } else {
                tweet.setCreatedAt(LocalDateTime.now()); // 如果created_at为null，使用当前时间
            }
            tweet.setType(0);
            tweet.setState(0);
            tweetRepository.save(tweet);
            tweets.add(String.format("作者: %s (粉丝数: %d)\n内容: %s\n发布时间: %s",
                    tweet.getAuthor(), followers, content, tweet.getCreatedAt()));
        }
    }

    private String callDeepSeekAPI(String content) {
        try {
            logger.info("调用DeepSeek API...");
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("https://api.deepseek.com/chat/completions"))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer sk-74408275249149f4a00bfce8821467ec")
                    .POST(HttpRequest.BodyPublishers.ofString(
                            String.format("{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"user\",\"content\":\"请分析以下推文内容: %s,如果是区块链的新协议或新东西，需要分析x社区对这个新协议或者新东西的关注，哪些KOL关注，官推的粉丝等等，技术优势。如果不是区块链直接说不是区块链方向不分析,还有不提到新协议或者新东西的名称也不分析\"}]}", content)
                    ))
                    .build();

            HttpResponse<String> response = TwitterAPIUtil.HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("DeepSeek API返回数据: {}", response.body());
            JsonNode rootNode = objectMapper.readTree(response.body());
            return rootNode.path("choices").path(0).path("message").path("content").asText();
        } catch (Exception e) {
            logger.error("调用DeepSeek API失败", e);
            return "AI分析失败: " + e.getMessage();
        }
    }


//    private void processTweets(List<String> tweets) {
//        // 这里可以添加处理逻辑，比如关键词匹配、发送通知等
//        //符合条件的推特重新的
//        notificationService.processNotify(tweets);
//    }

    private void processSearchTweets(List<String> tweets) {
        // 原有的关键词匹配处理
        notificationService.processNotify(tweets);

        // 新增：使用Gemini AI分析mint相关内容
        processMintTweets(tweets);
    }

    /**
     * 处理mint相关推文
     */
    private void processMintTweets(List<String> tweets) {
        try {
            logger.info("开始分析{}条推文的mint内容", tweets.size());

            List<String> mintTweets = new ArrayList<>();
            List<String> analysisResults = new ArrayList<>();

            for (String tweetContent : tweets) {
                try {
                    // 使用Gemini分析推文
                    TweetAnalysisResult result = geminiService.analyzeTweetForMint(tweetContent);

                    if (result.isShouldNotify()) {
                        mintTweets.add(tweetContent);

                        // 构建详细的分析结果
                        StringBuilder analysisDetail = new StringBuilder();
                        analysisDetail.append("🚀 发现mint机会！\n\n");
                        analysisDetail.append("推文内容：\n").append(tweetContent).append("\n\n");
                        analysisDetail.append("分析结果：\n");
                        analysisDetail.append("- 包含mint内容：").append(result.isContainsMintContent() ? "✅" : "❌").append("\n");
                        analysisDetail.append("- 包含网址：").append(result.isContainsUrl() ? "✅" : "❌").append("\n");
                        analysisDetail.append("- 置信度：").append(result.getConfidence()).append("%\n");

                        if (!result.getExtractedUrls().isEmpty()) {
                            analysisDetail.append("- 发现链接：\n");
                            for (String url : result.getExtractedUrls()) {
                                analysisDetail.append("  • ").append(url).append("\n");
                            }
                        }

                        if (!result.getMintKeywords().isEmpty()) {
                            analysisDetail.append("- 关键词：").append(String.join(", ", result.getMintKeywords())).append("\n");
                        }

                        analysisDetail.append("- AI分析：").append(result.getAnalysisDetails()).append("\n");
                        analysisDetail.append("- 分析原因：").append(result.getReason()).append("\n");

                        analysisResults.add(analysisDetail.toString());

                        logger.info("发现符合条件的mint推文，置信度: {}%", result.getConfidence());
                    }
                } catch (Exception e) {
                    logger.error("分析单条推文失败: {}", tweetContent.substring(0, Math.min(50, tweetContent.length())), e);
                }
            }

            // 如果有符合条件的mint推文，立即发送邮件通知
            if (!mintTweets.isEmpty()) {
                logger.info("发现{}条符合条件的mint推文，准备发送邮件通知", mintTweets.size());

                // 发送详细分析结果
                notificationService.sendNotification(analysisResults, "🚀 Mint机会提醒 - AI智能分析");

                logger.info("mint推文邮件通知已发送，共{}条推文", mintTweets.size());
            } else {
                logger.info("本次分析未发现符合条件的mint推文");
            }

        } catch (Exception e) {
            logger.error("处理mint推文时发生错误", e);
        }
    }

    @Scheduled(cron = "0 0 9-23 * * *") // 每小时整点执行一次
    public void sendHourlyTweetsSummary() {
        try {
            // 获取最近一小时的推文
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            List<Tweet> recentTweets = tweetRepository.findRecentTweets(oneHourAgo);

            if (!recentTweets.isEmpty()) {
                // 格式化邮件内容
                StringBuilder emailContent = new StringBuilder();
                emailContent.append("最近一小时内的推文摘要:\n\n");

                for (Tweet tweet : recentTweets) {
                    emailContent.append("作者: ").append(tweet.getAuthor())
                            .append(" (粉丝数: ").append(tweet.getFollowers()).append(")\n")
                            .append("内容: ").append(tweet.getContent()).append("\n")
//                            .append("AI分析: ").append(tweet.getAiAnalysis()).append("\n")
                            .append("发布时间: ").append(tweet.getCreatedAt()).append("\n")
                            .append("\n\n++++++++++++新消息++++++++++++\n\n");
                }
                // 发送邮件通知
                notificationService.sendNotification(Collections.singletonList(emailContent.toString()), "1小时推文摘要");
                logger.info("已发送最近一小时的推文摘要邮件，共{}条推文", recentTweets.size());
            } else {
                logger.info("最近一小时没有新推文，不发送邮件");
            }
        } catch (Exception e) {
            logger.error("发送每小时推文摘要失败: " + e.getMessage());
        }
    }

    @Scheduled(cron = "0 0 2,5,8 * * *") // 每3小时整点执行一次
    public void sendHourlyTweetsSummaryforNight() {
        try {
            // 获取最近一小时的推文
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(3);
            List<Tweet> recentTweets = tweetRepository.findRecentTweets(oneHourAgo);

            if (!recentTweets.isEmpty()) {
                // 格式化邮件内容
                StringBuilder emailContent = new StringBuilder();
                emailContent.append("最近三小时内的推文摘要:\n\n");

                for (Tweet tweet : recentTweets) {
                    emailContent.append("作者: ").append(tweet.getAuthor())
                            .append(" (粉丝数: ").append(tweet.getFollowers()).append(")\n")
                            .append("内容: ").append(tweet.getContent()).append("\n")
                            .append("发布时间: ").append(tweet.getCreatedAt()).append("\n")
                            .append("\n\n++++++++++++新消息++++++++++++\n\n");
                }
                // 发送邮件通知
                notificationService.sendNotification(Collections.singletonList(emailContent.toString()), "3小时推文摘要");
                logger.info("已发送最近三小时的推文摘要邮件，共{}条推文", recentTweets.size());
            } else {
                logger.info("最近三小时没有新推文，不发送邮件");
            }
        } catch (Exception e) {
            logger.error("发送3小时推文摘要失败: " + e.getMessage());
        }
    }
}