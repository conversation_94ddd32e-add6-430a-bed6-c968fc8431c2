# Twitter API Key池管理系统使用说明

## 概述

Twitter API Key池管理系统允许你配置多个Twitter API keys，系统会自动轮换使用这些keys，每个key达到指定的使用次数限制后会自动切换到下一个key。

## 配置说明

### 1. 在 `application.properties` 中配置多个API keys

```properties
# Twitter API Key池配置
twitter.api.pool.enabled=true
twitter.api.pool.max-requests-per-key=1000
twitter.api.pool.reset-interval-hours=24
twitter.api.pool.keys[0]=your_first_api_key_here
twitter.api.pool.keys[1]=your_second_api_key_here
twitter.api.pool.keys[2]=your_third_api_key_here
twitter.api.pool.keys[3]=your_fourth_api_key_here
```

### 2. 配置参数说明

- `twitter.api.pool.enabled`: 是否启用key池功能（默认: true）
- `twitter.api.pool.max-requests-per-key`: 每个key的最大请求次数（默认: 1000）
- `twitter.api.pool.reset-interval-hours`: key使用统计重置间隔，单位小时（默认: 24）
- `twitter.api.pool.keys[n]`: API keys列表，可以配置任意数量的keys

## 功能特性

### 1. 自动轮换
- 当前key达到使用限制时，自动切换到下一个可用key
- 支持循环使用所有配置的keys
- 使用Redis存储key使用统计和当前索引

### 2. 使用计数
- 每次API调用都会记录对应key的使用次数
- 支持设置每个key的最大使用次数
- 使用计数会在指定时间后自动重置

### 3. 状态监控
- 提供API接口查看所有keys的使用状态
- 支持查看当前使用的key和使用次数
- 提供重置功能清空所有key的使用计数

## API接口

### 1. 查看Key池状态
```
GET /api/twitter-key-pool/status
```

响应示例：
```json
{
  "status": "Twitter API Key池状态:\nKey 1: 7aa3****cb7c - 使用次数: 245/1000 [当前]\nKey 2: 8bb4****dc8d - 使用次数: 0/1000\n",
  "timestamp": 1699123456789
}
```

### 2. 查看当前使用的Key
```
GET /api/twitter-key-pool/current-key
```

响应示例：
```json
{
  "currentKey": "7aa3****cb7c",
  "usage": 245,
  "timestamp": 1699123456789
}
```

### 3. 重置所有Key使用计数
```
POST /api/twitter-key-pool/reset
```

### 4. 测试Key池功能
```
POST /api/twitter-key-pool/test
```

## 使用方式

### 1. 代码中使用

原来的静态方法调用：
```java
// 旧方式（已废弃）
JsonNode response = TwitterAPIUtil.getTwitterListUsers(listId);
```

新的实例方法调用：
```java
// 新方式（推荐）
@Autowired
private TwitterAPIUtil twitterAPIUtil;

JsonNode response = twitterAPIUtil.getTwitterListUsers(listId);
```

### 2. 自动切换逻辑

系统会在以下情况自动切换到下一个key：
1. 当前key使用次数达到配置的最大值
2. 当前key不可用或为空

### 3. 向后兼容

系统保留了原有的静态方法，但标记为`@Deprecated`，建议迁移到新的实例方法。

## 监控和维护

### 1. 日志监控

系统会记录以下关键日志：
- Key池初始化状态
- Key切换事件
- API使用统计
- 错误和异常情况

### 2. Redis存储

Key使用统计存储在Redis中，key格式：
- `twitter:api:key:{api_key}`: 存储对应key的使用次数
- `twitter:api:current_index`: 存储当前使用的key索引

### 3. 定期维护

- 使用计数会根据配置的重置间隔自动清零
- 可以通过API手动重置所有key的使用计数
- 建议定期检查key池状态确保正常运行

## 故障排除

### 1. Key池未生效
- 检查 `twitter.api.pool.enabled` 是否为 true
- 确认至少配置了一个有效的API key
- 检查Redis连接是否正常

### 2. Key切换异常
- 查看应用日志中的错误信息
- 检查所有配置的keys是否有效
- 确认Redis中的数据是否正确

### 3. 使用计数异常
- 检查Redis连接和权限
- 确认时间配置是否正确
- 可以手动重置计数进行测试

## 最佳实践

1. **合理配置key数量**: 根据API使用频率配置足够的keys
2. **监控使用情况**: 定期检查key池状态，确保有足够的可用keys
3. **设置合理限制**: 根据API提供商的限制设置合理的使用次数
4. **备份配置**: 保存好所有API keys的备份
5. **定期更新**: 及时更新过期或失效的API keys
