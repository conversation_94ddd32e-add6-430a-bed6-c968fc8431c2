# 钱包管理系统 API 使用说明

## 概述

钱包管理系统提供多链钱包管理功能，支持以太坊(EVM)、比特币(BTC)和Solana(SOL)三条主要区块链。

## 核心特性

- **智能缓存**: 余额信息缓存5分钟，提高查询速度
- **定时更新**: 每5分钟自动更新所有钱包余额
- **即时查询**: 新添加钱包时立即查询链上余额
- **多链支持**: 支持EVM、BTC、SOLANA三条链
- **真实数据**: 使用CoinGecko API获取真实市场数据，通过RPC获取真实链上余额

## API 接口

### 钱包管理

#### 1. 获取用户钱包列表
```
GET /v1/wallets?userId=1
```

#### 2. 添加钱包（会立即查询链上余额）
```
POST /v1/wallets?userId=1
Content-Type: application/json

{
  "chain": "EVM",
  "address": "******************************************",
  "name": "主钱包",
  "notes": "日常使用"
}
```

#### 3. 更新钱包信息
```
PUT /v1/wallets/{walletId}?userId=1
Content-Type: application/json

{
  "name": "更新后的名称",
  "notes": "更新后的备注"
}
```

#### 4. 删除钱包
```
DELETE /v1/wallets/{walletId}?userId=1
```

#### 5. 批量删除钱包
```
DELETE /v1/wallets/batch?userId=1
Content-Type: application/json

{
  "walletIds": ["1", "2", "3"]
}
```

### 资产查询（从缓存和数据库）

#### 1. 刷新钱包余额
```
POST /v1/wallets/{walletId}/refresh?userId=1
```

#### 2. 刷新所有钱包
```
POST /v1/wallets/refresh-all?userId=1
```

### 统计数据

#### 1. 获取资产总览
```
GET /v1/wallets/overview?userId=1
```

#### 2. 获取资产趋势
```
GET /v1/wallets/trends?userId=1&timeRange=7d
```

### 市场数据

#### 1. 获取代币价格
```
GET /v1/market/prices?symbols=ETH,BTC,SOL
```

## 数据流程

### 添加钱包流程
1. 用户调用添加钱包API
2. 系统验证地址格式
3. 立即查询链上余额（第一次）
4. 保存钱包信息到数据库
5. 缓存余额信息到Redis

### 定时更新流程
1. 每5分钟执行定时任务
2. 查询所有钱包地址
3. 批量查询链上余额
4. 更新数据库记录
5. 更新Redis缓存

### 查询余额流程
1. 用户调用查询API
2. 优先从Redis缓存获取
3. 缓存未命中则从数据库获取
4. 返回余额信息

## 支持的区块链

| 链名称 | 标识符 | 地址格式 | 示例 |
|--------|--------|----------|------|
| Ethereum | `EVM` | 0x开头，42字符 | `******************************************` |
| Bitcoin | `BTC` | bc1/1/3开头 | `******************************************` |
| Solana | `SOLANA` | Base58编码，32-44字符 | `DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK` |

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "WALLET_NOT_FOUND",
    "message": "钱包不存在"
  },
  "timestamp": 1640995200000
}
```

## 错误代码

| 错误代码 | 描述 |
|---------|------|
| USER_NOT_FOUND | 用户不存在 |
| WALLET_NOT_FOUND | 钱包不存在 |
| INVALID_ADDRESS | 无效的钱包地址 |
| DUPLICATE_WALLET | 钱包已存在 |
| CHAIN_NOT_SUPPORTED | 不支持的区块链 |
| VALIDATION_ERROR | 参数验证失败 |

## 使用示例

### 完整流程示例

1. **添加钱包**
```bash
curl -X POST "http://localhost:8080/v1/wallets?userId=1" \
  -H "Content-Type: application/json" \
  -d '{"chain":"EVM","address":"******************************************","name":"主钱包","notes":"日常使用"}'
```

2. **查询钱包列表**
```bash
curl "http://localhost:8080/v1/wallets?userId=1"
```

3. **获取资产总览**
```bash
curl "http://localhost:8080/v1/wallets/overview?userId=1"
```

## 注意事项

1. **缓存机制**: 余额数据缓存5分钟，查询接口优先返回缓存数据
2. **定时更新**: 系统每5分钟自动更新所有钱包余额
3. **链上查询**: 只有添加钱包和定时任务会查询链上数据
4. **地址验证**: 系统会验证钱包地址格式的正确性
5. **批量处理**: 定时任务采用批量处理，避免频繁请求

## 配置说明

在 `application.properties` 中可以配置：

```properties
# 钱包缓存配置
wallet.cache.enabled=true
wallet.cache.balance-expire-minutes=5
wallet.cache.batch-size=50
wallet.cache.request-interval-ms=100

# 区块链RPC配置
blockchain.ethereum.rpc-url=https://mainnet.infura.io/v3/your_project_id
blockchain.bitcoin.api-url=https://blockstream.info/api
blockchain.solana.rpc-url=https://api.mainnet-beta.solana.com

# 市场数据API配置
market.coingecko.api-url=https://api.coingecko.com/api/v3
market.coingecko.api-key=your_coingecko_api_key
```

## 真实数据源

### 市场数据
- **CoinGecko API**: 获取代币价格、24小时涨跌幅、市值、交易量等数据
- **支持代币**: ETH, BTC, SOL, USDT, USDC, BNB等主流代币

### 区块链数据
- **以太坊**: 通过Infura或其他RPC节点获取ETH余额和ERC-20代币
- **比特币**: 通过Blockstream API获取BTC余额
- **Solana**: 通过官方RPC节点获取SOL余额和SPL代币
