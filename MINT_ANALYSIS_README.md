# 推文Mint内容分析功能

## 功能概述

本功能基于LangChain4j和Google Gemini AI实现，能够智能分析推文内容，识别是否包含NFT或代币铸造(mint)相关信息，并在符合条件时自动发送邮件通知。

## 主要特性

1. **智能内容识别**：使用Gemini AI分析推文内容，判断是否为真实的mint活动
2. **网址检测**：自动提取推文中的链接，确保包含有效的mint网站
3. **垃圾过滤**：过滤广告、抽奖、互关等无关内容
4. **实时通知**：符合条件的推文立即通过邮件推送
5. **置信度评估**：AI分析结果包含置信度评分
6. **批量处理**：支持批量分析多条推文

## 配置说明

### 1. Gemini API配置

在 `application.properties` 中添加以下配置：

```properties
# Gemini AI 配置
langchain4j.google-ai-gemini.api-key=YOUR_GEMINI_API_KEY_HERE
langchain4j.google-ai-gemini.model-name=gemini-1.5-flash
langchain4j.google-ai-gemini.temperature=0.7
langchain4j.google-ai-gemini.max-tokens=1000

# 推文分析配置
tweet.analysis.enabled=true
tweet.analysis.confidence-threshold=70
tweet.analysis.batch-size=10
```

### 2. 获取Gemini API Key

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API Key
3. 将API Key替换配置文件中的 `YOUR_GEMINI_API_KEY_HERE`

## 使用方式

### 1. 自动分析（推荐）

系统会自动在推文搜索任务中集成mint内容分析：

- 每次搜索推文时自动分析mint内容
- 符合条件的推文立即发送邮件通知
- 邮件包含详细的分析结果和链接信息

### 2. API测试接口

#### 分析单条推文
```bash
POST /api/tweet-analysis/analyze
Content-Type: application/json

{
  "content": "🚀 New NFT collection minting now! Get yours at https://example.com/mint"
}
```

#### 批量分析推文
```bash
POST /api/tweet-analysis/batch-analyze
Content-Type: application/json

{
  "contents": [
    "NFT mint is live! https://example.com/mint",
    "Follow me for more updates",
    "Token minting starts tomorrow https://token.example.com"
  ]
}
```

#### 分析并发送邮件通知
```bash
POST /api/tweet-analysis/analyze-and-notify
Content-Type: application/json

{
  "content": "🚀 New NFT collection minting now! Get yours at https://example.com/mint"
}
```

#### 快速检查
```bash
POST /api/tweet-analysis/quick-check
Content-Type: application/json

{
  "content": "NFT mint is live!"
}
```

## 分析标准

### 符合条件的推文需要满足：

1. **包含mint关键词**：
   - mint, minting, 铸造, 铸币
   - mint now, mint live, mint soon
   - free mint, public mint, whitelist mint
   - nft mint, token mint 等

2. **包含有效链接**：
   - 推文中必须包含 http/https 链接
   - 链接通常指向mint网站或官方页面

3. **非垃圾内容**：
   - 不包含关注、点赞、转发等互动请求
   - 不是抽奖、空投等活动
   - 不是纯广告或推广内容

4. **AI置信度**：
   - Gemini AI分析置信度 ≥ 70%
   - AI判断为真实的mint活动
   - AI判断为重要信息

## 邮件通知格式

符合条件的推文会发送包含以下信息的邮件：

```
🚀 发现mint机会！

推文内容：
[原始推文内容]

分析结果：
- 包含mint内容：✅
- 包含网址：✅
- 置信度：85%
- 发现链接：
  • https://example.com/mint
- 关键词：mint, nft mint
- AI分析：这是一个真实的NFT铸造活动...
- 分析原因：符合mint推送条件
```

## 日志监控

系统会记录详细的分析日志：

```
2024-01-01 10:00:00 INFO  - 开始分析3条推文的mint内容
2024-01-01 10:00:01 INFO  - 发现符合条件的mint推文，置信度: 85%
2024-01-01 10:00:02 INFO  - 发现1条符合条件的mint推文，准备发送邮件通知
2024-01-01 10:00:03 INFO  - mint推文邮件通知已发送，共1条推文
```

## 故障排除

### 1. API Key问题
- 确保Gemini API Key正确配置
- 检查API Key是否有效且有足够配额
- 查看日志中的错误信息

### 2. 分析结果不准确
- 调整 `temperature` 参数（0.1-1.0）
- 增加 `max-tokens` 限制
- 检查推文内容是否包含必要信息

### 3. 邮件发送失败
- 检查邮件配置是否正确
- 确认网络连接正常
- 查看邮件服务器日志

## 性能优化

1. **批量处理**：系统支持批量分析，减少API调用次数
2. **缓存机制**：可以考虑对分析结果进行缓存
3. **异步处理**：分析过程不会阻塞主要的推文搜索流程
4. **错误恢复**：单条推文分析失败不会影响其他推文的处理

## 扩展功能

未来可以考虑添加：

1. **自定义关键词**：允许用户自定义mint相关关键词
2. **白名单/黑名单**：支持特定账户的过滤规则
3. **多语言支持**：支持更多语言的mint内容识别
4. **历史记录**：保存分析历史和统计信息
5. **实时监控**：提供分析状态的实时监控界面
